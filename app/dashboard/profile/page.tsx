'use client';

import { fetchPortalUrl } from '@/api/stripePortal';
import { RouterPaths } from '@/common/routerPaths';
import { ButtonBorderSmall } from '@/components/Buttons';
import { ItemBox } from '@/components/ItemBox';
import { LinkGradientSmall } from '@/components/Links';
import { LoaderSpinnerSmall } from '@/components/Loaders';
import { useGetDeploymentCreditsLeft } from '@/hooks/useGetDeploymentCreditsLeft';
import { useUserStore } from '@/store/userStore';
import { DeploymentType } from '@/types/deployments';
import { SelectValue } from '@/types/selectValue';
import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

const ProfilePage = () => {
    const { user } = useUserStore();
    const router = useRouter();
    const { decoratedConfigs: decoratedConfigsIssuer } = useGetDeploymentCreditsLeft({ type: DeploymentType.ISSUER });
    const { decoratedConfigs: decoratedConfigsVerifier } = useGetDeploymentCreditsLeft({
        type: DeploymentType.VERIFIER,
    });

    const formatLabel = (key: string): string => {
        return key
            .replace(/([A-Z])/g, ' $1') // Dodaj spację przed wielką literą
            .replace(/^./, str => str.toUpperCase()) // Pierwsza litera wielka
            .trim(); // Usuń ewentualne spacje na początku/końcu
    };

    const parseCredits = (item: SelectValue) => {
        const labelLower = item.label.split(' (')[0].toLowerCase();
        const label = labelLower.charAt(0).toUpperCase() + labelLower.slice(1); // "Mainnet" lub "Testnet"
        const value = item.label.match(/\((.+)\)/)?.[1] || ''; // "99 left"

        return {
            label,
            value,
        };
    };

    const handlePortalClick = useCallback(async () => {
        try {
            const url = await fetchPortalUrl();
            if (!url) {
            } else {
                window.open(url, '_blank');
            }
        } catch {
            router.replace(RouterPaths.PAYMENT);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (!user)
        return (
            <ItemBox>
                <div className="flex justify-center items-center">
                    <LoaderSpinnerSmall />
                </div>
            </ItemBox>
        );
    return (
        <div className="w-fit mx-auto">
            <ItemBox>
                <div className="w-box-100 p-box-100 flex flex-col">
                    <h1 className="text-2xl">Profile</h1>
                    {Object.keys(user).map((item, index, array) => {
                        const data = user[item as keyof typeof user];
                        if (typeof data === 'string') {
                            const isLast = index === array.length - 1;
                            return (
                                <div key={index} className={clsx('py-4', { 'border-b border-main-600/10': !isLast })}>
                                    <label className="text-xs text-main-600/50">{formatLabel(item)}</label>
                                    <p>{data}</p>
                                </div>
                            );
                        } else return null;
                    })}
                    <div className="py-4 border-b border-main-600/10">
                        <div className="flex flex-row justify-between">
                            {decoratedConfigsIssuer.map((item, index) => {
                                const { label, value } = parseCredits(item);
                                return (
                                    <div key={`issuer-${index}`} className="flex-1">
                                        <label className="text-xs text-main-600/50">Issuer {label} Credits</label>
                                        <p>{value}</p>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                    <div className="py-4">
                        <div className="flex flex-row justify-between">
                            {decoratedConfigsVerifier.map((item, index) => {
                                const { label, value } = parseCredits(item);
                                return (
                                    <div key={`verifier-${index}`} className="flex-1">
                                        <label className="text-xs text-main-600/50">Verifier {label} Credits</label>
                                        <p>{value}</p>
                                    </div>
                                );
                            })}
                        </div>
                    </div>

                    <div className="flex flex-row gap-4 pt-4">
                        <ButtonBorderSmall onClick={handlePortalClick}>Manage Subscription</ButtonBorderSmall>
                        <LinkGradientSmall href={RouterPaths.CHANGE_PASSWORD}>Reset Password</LinkGradientSmall>
                    </div>
                </div>
            </ItemBox>
        </div>
    );
};

export default ProfilePage;
