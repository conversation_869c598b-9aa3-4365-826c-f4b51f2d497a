import { InputHTMLAttributes, ReactNode, useState, useEffect } from 'react';
import clsx from 'clsx';

// Country codes mapping for auto-detection
const COUNTRY_CODES = {
    '+1': { name: 'US/CA', maxLength: 10 },
    '+44': { name: 'UK', maxLength: 10 },
    '+49': { name: 'DE', maxLength: 11 },
    '+33': { name: 'FR', maxLength: 10 },
    '+39': { name: 'IT', maxLength: 10 },
    '+34': { name: 'ES', maxLength: 9 },
    '+48': { name: 'P<PERSON>', maxLength: 9 },
    '+420': { name: 'C<PERSON>', maxLength: 9 },
    '+421': { name: 'SK', maxLength: 9 },
    '+380': { name: 'UA', maxLength: 9 },
    '+7': { name: 'RU', maxLength: 10 },
};

interface Props extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type' | 'onChange'> {
    label?: string;
    labelNode?: ReactNode;
    disabled?: boolean;
    onChange?: (fullNumber: string, countryCode: string, phoneNumber: string) => void;
    defaultCountryCode?: string;
    value?: string; // Full phone number with country code
}

export const PhoneNumberInput = ({
    className,
    disabled,
    label,
    labelNode,
    onChange,
    defaultCountryCode = '+48',
    value = '',
    ...props
}: Props) => {
    const [countryCode, setCountryCode] = useState(defaultCountryCode);
    const [phoneValue, setPhoneValue] = useState('');

    // Parse initial value
    useEffect(() => {
        if (value) {
            const detectedCode = detectCountryCode(value);
            if (detectedCode) {
                setCountryCode(detectedCode);
                setPhoneValue(value.replace(detectedCode, '').trim());
            } else {
                setPhoneValue(value);
            }
        }
    }, [value]);

    const detectCountryCode = (fullNumber: string): string | null => {
        // Sort by length (longest first) to match longer codes first
        const sortedCodes = Object.keys(COUNTRY_CODES).sort((a, b) => b.length - a.length);

        for (const code of sortedCodes) {
            if (fullNumber.startsWith(code)) {
                return code;
            }
        }
        return null;
    };

    const formatPhoneNumber = (input: string, code: string) => {
        const digits = input.replace(/\D/g, '');
        const maxLength = COUNTRY_CODES[code as keyof typeof COUNTRY_CODES]?.maxLength || 15;

        // Limit digits based on country
        const limitedDigits = digits.slice(0, maxLength);

        // Format based on country (simple formatting for now)
        if (code === '+48') {
            // Polish format: XXX XXX XXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3)}`;
            return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3, 6)} ${limitedDigits.slice(6)}`;
        } else if (code === '+1') {
            // US format: (XXX) XXX-XXXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `(${limitedDigits.slice(0, 3)}) ${limitedDigits.slice(3)}`;
            return `(${limitedDigits.slice(0, 3)}) ${limitedDigits.slice(3, 6)}-${limitedDigits.slice(6)}`;
        } else {
            // Default format: XXX XXX XXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3)}`;
            return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3, 6)} ${limitedDigits.slice(6)}`;
        }
    };

    const handleCountryCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newCode = e.target.value;
        setCountryCode(newCode);

        // Reformat phone number for new country
        const cleanPhone = phoneValue.replace(/\D/g, '');
        const formatted = formatPhoneNumber(cleanPhone, newCode);
        setPhoneValue(formatted);

        onChange?.(newCode + cleanPhone, newCode, cleanPhone);
    };

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const input = e.target.value;
        const formatted = formatPhoneNumber(input, countryCode);
        const cleanPhone = formatted.replace(/\D/g, '');

        setPhoneValue(formatted);
        onChange?.(countryCode + cleanPhone, countryCode, cleanPhone);
    };

    return (
        <div className="flex flex-col gap-1">
            {label && <label className="block text-main-800 text-sm">{label}</label>}
            {labelNode && <>{labelNode}</>}
            <div className="flex gap-2">
                {/* Country Code Input */}
                <div className="relative w-24">
                    <input
                        type="text"
                        disabled={disabled}
                        value={countryCode}
                        onChange={handleCountryCodeChange}
                        placeholder="+48"
                        className={clsx(
                            'disabled:opacity-80',
                            'focus:outline-none focus:border-2',
                            'bg-main-600/5 border !text-main-600 w-full border-main-1100/50 rounded-lg p-3 h-12',
                            'text-center font-medium',
                            className
                        )}
                    />
                </div>

                {/* Phone Number Input */}
                <div className="relative flex-1">
                    <input
                        {...props}
                        type="tel"
                        disabled={disabled}
                        value={phoneValue}
                        onChange={handlePhoneChange}
                        placeholder="123 456 789"
                        className={clsx(
                            'disabled:opacity-80',
                            'focus:outline-none focus:border-2',
                            'bg-main-600/5 border !text-main-600 w-full border-main-1100/50 rounded-lg p-3 h-12',
                            className
                        )}
                    />
                </div>
            </div>

            {/* Country Info Display */}
            {COUNTRY_CODES[countryCode as keyof typeof COUNTRY_CODES] && (
                <div className="text-xs text-main-600/70 mt-1">
                    {COUNTRY_CODES[countryCode as keyof typeof COUNTRY_CODES].name}
                </div>
            )}
        </div>
    );
};
