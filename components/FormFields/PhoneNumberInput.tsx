import { InputHTMLAttributes, ReactNode, useState, useEffect, useRef } from 'react';
import clsx from 'clsx';
import Image from 'next/image';

// Country codes mapping for auto-detection
const COUNTRY_CODES = {
    // North America
    '+1': { name: 'US/CA', maxLength: 10 },

    // Europe
    '+44': { name: 'UK', maxLength: 10 },
    '+49': { name: 'Germany', maxLength: 11 },
    '+33': { name: 'France', maxLength: 10 },
    '+39': { name: 'Italy', maxLength: 10 },
    '+34': { name: 'Spain', maxLength: 9 },
    '+48': { name: 'Poland', maxLength: 9 },
    '+31': { name: 'Netherlands', maxLength: 9 },
    '+32': { name: 'Belgium', maxLength: 9 },
    '+41': { name: 'Switzerland', maxLength: 9 },
    '+43': { name: 'Austria', maxLength: 11 },
    '+45': { name: 'Denmark', maxLength: 8 },
    '+46': { name: 'Sweden', maxLength: 9 },
    '+47': { name: 'Norway', maxLength: 8 },
    '+358': { name: 'Finland', maxLength: 10 },
    '+420': { name: 'Czech Republic', maxLength: 9 },
    '+421': { name: 'Slovakia', maxLength: 9 },
    '+380': { name: 'Ukraine', maxLength: 9 },
    '+7': { name: 'Russia', maxLength: 10 },
    '+36': { name: 'Hungary', maxLength: 9 },
    '+40': { name: 'Romania', maxLength: 9 },
    '+359': { name: 'Bulgaria', maxLength: 9 },
    '+385': { name: 'Croatia', maxLength: 9 },
    '+386': { name: 'Slovenia', maxLength: 8 },
    '+381': { name: 'Serbia', maxLength: 9 },
    '+382': { name: 'Montenegro', maxLength: 8 },
    '+387': { name: 'Bosnia', maxLength: 8 },
    '+389': { name: 'North Macedonia', maxLength: 8 },
    '+30': { name: 'Greece', maxLength: 10 },
    '+351': { name: 'Portugal', maxLength: 9 },
    '+353': { name: 'Ireland', maxLength: 9 },
    '+354': { name: 'Iceland', maxLength: 7 },
    '+370': { name: 'Lithuania', maxLength: 8 },
    '+371': { name: 'Latvia', maxLength: 8 },
    '+372': { name: 'Estonia', maxLength: 8 },
    '+375': { name: 'Belarus', maxLength: 9 },
    '+373': { name: 'Moldova', maxLength: 8 },

    // Asia
    '+86': { name: 'China', maxLength: 11 },
    '+81': { name: 'Japan', maxLength: 11 },
    '+82': { name: 'South Korea', maxLength: 11 },
    '+91': { name: 'India', maxLength: 10 },
    '+92': { name: 'Pakistan', maxLength: 10 },
    '+93': { name: 'Afghanistan', maxLength: 9 },
    '+94': { name: 'Sri Lanka', maxLength: 9 },
    '+95': { name: 'Myanmar', maxLength: 10 },
    '+98': { name: 'Iran', maxLength: 10 },
    '+90': { name: 'Turkey', maxLength: 10 },
    '+972': { name: 'Israel', maxLength: 9 },
    '+971': { name: 'UAE', maxLength: 9 },
    '+966': { name: 'Saudi Arabia', maxLength: 9 },
    '+974': { name: 'Qatar', maxLength: 8 },
    '+973': { name: 'Bahrain', maxLength: 8 },
    '+965': { name: 'Kuwait', maxLength: 8 },
    '+968': { name: 'Oman', maxLength: 8 },
    '+967': { name: 'Yemen', maxLength: 9 },
    '+964': { name: 'Iraq', maxLength: 10 },
    '+962': { name: 'Jordan', maxLength: 9 },
    '+961': { name: 'Lebanon', maxLength: 8 },
    '+963': { name: 'Syria', maxLength: 9 },
    '+60': { name: 'Malaysia', maxLength: 10 },
    '+65': { name: 'Singapore', maxLength: 8 },
    '+66': { name: 'Thailand', maxLength: 9 },
    '+84': { name: 'Vietnam', maxLength: 10 },
    '+62': { name: 'Indonesia', maxLength: 12 },
    '+63': { name: 'Philippines', maxLength: 10 },
    '+855': { name: 'Cambodia', maxLength: 9 },
    '+856': { name: 'Laos', maxLength: 10 },
    '+880': { name: 'Bangladesh', maxLength: 10 },
    '+977': { name: 'Nepal', maxLength: 10 },
    '+975': { name: 'Bhutan', maxLength: 8 },
    '+960': { name: 'Maldives', maxLength: 7 },

    // Africa
    '+27': { name: 'South Africa', maxLength: 9 },
    '+20': { name: 'Egypt', maxLength: 10 },
    '+212': { name: 'Morocco', maxLength: 9 },
    '+213': { name: 'Algeria', maxLength: 9 },
    '+216': { name: 'Tunisia', maxLength: 8 },
    '+218': { name: 'Libya', maxLength: 9 },
    '+234': { name: 'Nigeria', maxLength: 10 },
    '+233': { name: 'Ghana', maxLength: 9 },
    '+254': { name: 'Kenya', maxLength: 9 },
    '+255': { name: 'Tanzania', maxLength: 9 },
    '+256': { name: 'Uganda', maxLength: 9 },
    '+251': { name: 'Ethiopia', maxLength: 9 },
    '+252': { name: 'Somalia', maxLength: 8 },
    '+250': { name: 'Rwanda', maxLength: 9 },
    '+257': { name: 'Burundi', maxLength: 8 },

    // Oceania
    '+61': { name: 'Australia', maxLength: 9 },
    '+64': { name: 'New Zealand', maxLength: 9 },
    '+679': { name: 'Fiji', maxLength: 7 },

    // South America
    '+55': { name: 'Brazil', maxLength: 11 },
    '+54': { name: 'Argentina', maxLength: 10 },
    '+56': { name: 'Chile', maxLength: 9 },
    '+57': { name: 'Colombia', maxLength: 10 },
    '+58': { name: 'Venezuela', maxLength: 10 },
    '+51': { name: 'Peru', maxLength: 9 },
    '+593': { name: 'Ecuador', maxLength: 9 },
    '+595': { name: 'Paraguay', maxLength: 9 },
    '+598': { name: 'Uruguay', maxLength: 8 },
    '+591': { name: 'Bolivia', maxLength: 8 },
    '+592': { name: 'Guyana', maxLength: 7 },
    '+597': { name: 'Suriname', maxLength: 7 },

    // Central America & Caribbean
    '+52': { name: 'Mexico', maxLength: 10 },
    '+502': { name: 'Guatemala', maxLength: 8 },
    '+503': { name: 'El Salvador', maxLength: 8 },
    '+504': { name: 'Honduras', maxLength: 8 },
    '+505': { name: 'Nicaragua', maxLength: 8 },
    '+506': { name: 'Costa Rica', maxLength: 8 },
    '+507': { name: 'Panama', maxLength: 8 },
    '+53': { name: 'Cuba', maxLength: 8 },
    '+1876': { name: 'Jamaica', maxLength: 7 },
    '+1809': { name: 'Dominican Rep.', maxLength: 7 },
    '+1787': { name: 'Puerto Rico', maxLength: 7 },
};

// Convert to options array for select
const COUNTRY_OPTIONS = Object.entries(COUNTRY_CODES).map(([code, info]) => ({
    value: code,
    label: `${code} (${info.name})`,
}));

interface Props extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type' | 'onChange'> {
    label?: string;
    labelNode?: ReactNode;
    disabled?: boolean;
    onChange?: (fullNumber: string, countryCode: string, phoneNumber: string) => void;
    defaultCountryCode?: string;
    value?: string; // Full phone number with country code
}

export const PhoneNumberInput = ({
    className,
    disabled,
    label,
    labelNode,
    onChange,
    defaultCountryCode = '+48',
    value = '',
    ...props
}: Props) => {
    const [countryCode, setCountryCode] = useState(defaultCountryCode);
    const [phoneValue, setPhoneValue] = useState('');
    const [isSelectOpen, setIsSelectOpen] = useState(false);
    const selectRef = useRef<HTMLDivElement>(null);

    // Parse initial value
    useEffect(() => {
        if (value) {
            const detectedCode = detectCountryCode(value);
            if (detectedCode) {
                setCountryCode(detectedCode);
                setPhoneValue(value.replace(detectedCode, '').trim());
            } else {
                setPhoneValue(value);
            }
        }
    }, [value]);

    // Handle click outside to close select
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
                setIsSelectOpen(false);
            }
        };

        if (isSelectOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            return () => document.removeEventListener('mousedown', handleClickOutside);
        }
    }, [isSelectOpen]);

    const detectCountryCode = (fullNumber: string): string | null => {
        // Sort by length (longest first) to match longer codes first
        const sortedCodes = Object.keys(COUNTRY_CODES).sort((a, b) => b.length - a.length);

        for (const code of sortedCodes) {
            if (fullNumber.startsWith(code)) {
                return code;
            }
        }
        return null;
    };

    const formatPhoneNumber = (input: string, code: string) => {
        const digits = input.replace(/\D/g, '');
        const maxLength = COUNTRY_CODES[code as keyof typeof COUNTRY_CODES]?.maxLength || 15;

        // Limit digits based on country
        const limitedDigits = digits.slice(0, maxLength);

        // Format based on country (simple formatting for now)
        if (code === '+48') {
            // Polish format: XXX XXX XXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3)}`;
            return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3, 6)} ${limitedDigits.slice(6)}`;
        } else if (code === '+1') {
            // US format: (XXX) XXX-XXXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `(${limitedDigits.slice(0, 3)}) ${limitedDigits.slice(3)}`;
            return `(${limitedDigits.slice(0, 3)}) ${limitedDigits.slice(3, 6)}-${limitedDigits.slice(6)}`;
        } else {
            // Default format: XXX XXX XXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3)}`;
            return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3, 6)} ${limitedDigits.slice(6)}`;
        }
    };

    const handleCountryCodeSelect = (code: string) => {
        setCountryCode(code);
        setIsSelectOpen(false);

        // Reformat phone number for new country
        const cleanPhone = phoneValue.replace(/\D/g, '');
        const formatted = formatPhoneNumber(cleanPhone, code);
        setPhoneValue(formatted);

        onChange?.(code + cleanPhone, code, cleanPhone);
    };

    const handleSelectToggle = () => {
        if (!disabled) {
            setIsSelectOpen(prev => !prev);
        }
    };

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const input = e.target.value;
        const formatted = formatPhoneNumber(input, countryCode);
        const cleanPhone = formatted.replace(/\D/g, '');

        setPhoneValue(formatted);
        onChange?.(countryCode + cleanPhone, countryCode, cleanPhone);
    };

    const selectedCountry = COUNTRY_OPTIONS.find(option => option.value === countryCode);

    return (
        <div className="flex flex-col gap-1">
            {label && <label className="block text-main-800 text-sm">{label}</label>}
            {labelNode && <>{labelNode}</>}
            <div className="flex gap-2">
                {/* Country Code Select */}
                <div className="relative w-32" ref={selectRef}>
                    <div
                        onClick={handleSelectToggle}
                        className={clsx(
                            'disabled:opacity-80 cursor-pointer',
                            'focus:outline-none focus:border-2',
                            'bg-main-600/5 border text-main-600 w-full border-main-1100/50 rounded-lg p-3 h-12',
                            'flex items-center justify-between',
                            disabled && 'cursor-not-allowed opacity-50',
                            className
                        )}
                        aria-disabled={disabled}
                    >
                        <span className="font-medium text-sm">{selectedCountry?.value || countryCode}</span>
                        <Image
                            height={16}
                            width={16}
                            alt=""
                            src="/icons/arrow.svg"
                            className={clsx(
                                'transition-transform duration-300',
                                isSelectOpen ? 'rotate-180' : 'rotate-0'
                            )}
                        />
                    </div>

                    {/* Dropdown */}
                    {isSelectOpen && (
                        <div className="absolute top-14 z-10 left-0 w-full max-h-48 overflow-y-auto bg-main-1300 border text-main-600 border-main-1100/50 rounded-lg p-2 shadow-lg">
                            {COUNTRY_OPTIONS.map(option => (
                                <div
                                    key={option.value}
                                    className="cursor-pointer hover:bg-main-600/10 p-2 rounded text-sm"
                                    onClick={() => handleCountryCodeSelect(option.value)}
                                >
                                    {option.label}
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Phone Number Input */}
                <div className="relative flex-1">
                    <input
                        {...props}
                        type="tel"
                        disabled={disabled}
                        value={phoneValue}
                        onChange={handlePhoneChange}
                        placeholder="123 456 789"
                        className={clsx(
                            'disabled:opacity-80',
                            'focus:outline-none focus:border-2',
                            'bg-main-600/5 border !text-main-600 w-full border-main-1100/50 rounded-lg p-3 h-12',
                            className
                        )}
                    />
                </div>
            </div>
        </div>
    );
};
