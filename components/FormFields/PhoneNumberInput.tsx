import { InputHTMLAttributes, ReactNode, useState, useEffect } from 'react';
import clsx from 'clsx';

interface Props extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type' | 'onChange'> {
    label?: string;
    labelNode?: ReactNode;
    disabled?: boolean;
    onChange?: (value: string) => void;
    countryCode?: string;
}

export const PhoneNumberInput = ({
    className,
    disabled,
    label,
    labelNode,
    onChange,
    countryCode = '+48',
    value,
    ...props
}: Props) => {
    const [phoneValue, setPhoneValue] = useState(value?.toString() || '');

    useEffect(() => {
        setPhoneValue(value?.toString() || '');
    }, [value]);

    const formatPhoneNumber = (input: string) => {
        // Remove all non-digits
        const digits = input.replace(/\D/g, '');

        // Format as XXX XXX XXX for Polish numbers
        if (digits.length <= 3) return digits;
        if (digits.length <= 6) return `${digits.slice(0, 3)} ${digits.slice(3)}`;
        return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)}`;
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const input = e.target.value;
        const formatted = formatPhoneNumber(input);

        // Limit to 9 digits (Polish phone number format)
        if (formatted.replace(/\s/g, '').length <= 9) {
            setPhoneValue(formatted);
            onChange?.(formatted.replace(/\s/g, ''));
        }
    };

    return (
        <div className="flex flex-col gap-1">
            {label && <label className="block text-main-800 text-sm">{label}</label>}
            {labelNode && <>{labelNode}</>}
            <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-main-600 font-medium">
                    {countryCode}
                </div>
                <input
                    {...props}
                    type="tel"
                    disabled={disabled}
                    value={phoneValue}
                    onChange={handleInputChange}
                    placeholder="123 456 789"
                    className={clsx(
                        'disabled:opacity-80',
                        'focus:outline-none focus:border-2',
                        'bg-main-600/5 border !text-main-600 w-full border-main-1100/50 rounded-lg p-3 h-12',
                        'pl-16', // Extra padding for country code
                        className
                    )}
                />
            </div>
        </div>
    );
};
