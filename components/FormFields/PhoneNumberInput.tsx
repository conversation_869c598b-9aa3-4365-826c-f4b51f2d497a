import { InputHTMLAttributes, ReactNode, useState, useEffect, useRef } from 'react';
import clsx from 'clsx';
import Image from 'next/image';

// Country codes mapping for auto-detection
const COUNTRY_CODES = {
    '+1': { name: 'US/CA', maxLength: 10 },
    '+44': { name: 'UK', maxLength: 10 },
    '+49': { name: 'DE', maxLength: 11 },
    '+33': { name: 'FR', maxLength: 10 },
    '+39': { name: 'IT', maxLength: 10 },
    '+34': { name: 'ES', maxLength: 9 },
    '+48': { name: 'PL', maxLength: 9 },
    '+420': { name: 'CZ', maxLength: 9 },
    '+421': { name: 'SK', maxLength: 9 },
    '+380': { name: 'UA', maxLength: 9 },
    '+7': { name: 'RU', maxLength: 10 },
};

// Convert to options array for select
const COUNTRY_OPTIONS = Object.entries(COUNTRY_CODES).map(([code, info]) => ({
    value: code,
    label: `${code} (${info.name})`,
}));

interface Props extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type' | 'onChange'> {
    label?: string;
    labelNode?: ReactNode;
    disabled?: boolean;
    onChange?: (fullNumber: string, countryCode: string, phoneNumber: string) => void;
    defaultCountryCode?: string;
    value?: string; // Full phone number with country code
}

export const PhoneNumberInput = ({
    className,
    disabled,
    label,
    labelNode,
    onChange,
    defaultCountryCode = '+48',
    value = '',
    ...props
}: Props) => {
    const [countryCode, setCountryCode] = useState(defaultCountryCode);
    const [phoneValue, setPhoneValue] = useState('');
    const [isSelectOpen, setIsSelectOpen] = useState(false);
    const selectRef = useRef<HTMLDivElement>(null);

    // Parse initial value
    useEffect(() => {
        if (value) {
            const detectedCode = detectCountryCode(value);
            if (detectedCode) {
                setCountryCode(detectedCode);
                setPhoneValue(value.replace(detectedCode, '').trim());
            } else {
                setPhoneValue(value);
            }
        }
    }, [value]);

    // Handle click outside to close select
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
                setIsSelectOpen(false);
            }
        };

        if (isSelectOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            return () => document.removeEventListener('mousedown', handleClickOutside);
        }
    }, [isSelectOpen]);

    const detectCountryCode = (fullNumber: string): string | null => {
        // Sort by length (longest first) to match longer codes first
        const sortedCodes = Object.keys(COUNTRY_CODES).sort((a, b) => b.length - a.length);

        for (const code of sortedCodes) {
            if (fullNumber.startsWith(code)) {
                return code;
            }
        }
        return null;
    };

    const formatPhoneNumber = (input: string, code: string) => {
        const digits = input.replace(/\D/g, '');
        const maxLength = COUNTRY_CODES[code as keyof typeof COUNTRY_CODES]?.maxLength || 15;

        // Limit digits based on country
        const limitedDigits = digits.slice(0, maxLength);

        // Format based on country (simple formatting for now)
        if (code === '+48') {
            // Polish format: XXX XXX XXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3)}`;
            return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3, 6)} ${limitedDigits.slice(6)}`;
        } else if (code === '+1') {
            // US format: (XXX) XXX-XXXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `(${limitedDigits.slice(0, 3)}) ${limitedDigits.slice(3)}`;
            return `(${limitedDigits.slice(0, 3)}) ${limitedDigits.slice(3, 6)}-${limitedDigits.slice(6)}`;
        } else {
            // Default format: XXX XXX XXX
            if (limitedDigits.length <= 3) return limitedDigits;
            if (limitedDigits.length <= 6) return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3)}`;
            return `${limitedDigits.slice(0, 3)} ${limitedDigits.slice(3, 6)} ${limitedDigits.slice(6)}`;
        }
    };

    const handleCountryCodeSelect = (code: string) => {
        setCountryCode(code);
        setIsSelectOpen(false);

        // Reformat phone number for new country
        const cleanPhone = phoneValue.replace(/\D/g, '');
        const formatted = formatPhoneNumber(cleanPhone, code);
        setPhoneValue(formatted);

        onChange?.(code + cleanPhone, code, cleanPhone);
    };

    const handleSelectToggle = () => {
        if (!disabled) {
            setIsSelectOpen(prev => !prev);
        }
    };

    const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const input = e.target.value;
        const formatted = formatPhoneNumber(input, countryCode);
        const cleanPhone = formatted.replace(/\D/g, '');

        setPhoneValue(formatted);
        onChange?.(countryCode + cleanPhone, countryCode, cleanPhone);
    };

    const selectedCountry = COUNTRY_OPTIONS.find(option => option.value === countryCode);

    return (
        <div className="flex flex-col gap-1">
            {label && <label className="block text-main-800 text-sm">{label}</label>}
            {labelNode && <>{labelNode}</>}
            <div className="flex gap-2">
                {/* Country Code Select */}
                <div className="relative w-32" ref={selectRef}>
                    <div
                        onClick={handleSelectToggle}
                        className={clsx(
                            'disabled:opacity-80 cursor-pointer',
                            'focus:outline-none focus:border-2',
                            'bg-main-600/5 border text-main-600 w-full border-main-1100/50 rounded-lg p-3 h-12',
                            'flex items-center justify-between',
                            disabled && 'cursor-not-allowed opacity-50',
                            className
                        )}
                        aria-disabled={disabled}
                    >
                        <span className="font-medium text-sm">{selectedCountry?.value || countryCode}</span>
                        <Image
                            height={16}
                            width={16}
                            alt=""
                            src="/icons/arrow.svg"
                            className={clsx(
                                'transition-transform duration-300',
                                isSelectOpen ? 'rotate-180' : 'rotate-0'
                            )}
                        />
                    </div>

                    {/* Dropdown */}
                    {isSelectOpen && (
                        <div className="absolute top-14 z-10 left-0 w-full max-h-48 overflow-y-auto bg-main-1300 border text-main-600 border-main-1100/50 rounded-lg p-2 shadow-lg">
                            {COUNTRY_OPTIONS.map(option => (
                                <div
                                    key={option.value}
                                    className="cursor-pointer hover:bg-main-600/10 p-2 rounded text-sm"
                                    onClick={() => handleCountryCodeSelect(option.value)}
                                >
                                    {option.label}
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Phone Number Input */}
                <div className="relative flex-1">
                    <input
                        {...props}
                        type="tel"
                        disabled={disabled}
                        value={phoneValue}
                        onChange={handlePhoneChange}
                        placeholder="123 456 789"
                        className={clsx(
                            'disabled:opacity-80',
                            'focus:outline-none focus:border-2',
                            'bg-main-600/5 border !text-main-600 w-full border-main-1100/50 rounded-lg p-3 h-12',
                            className
                        )}
                    />
                </div>
            </div>

            {/* Country Info Display */}
            {COUNTRY_CODES[countryCode as keyof typeof COUNTRY_CODES] && (
                <div className="text-xs text-main-600/70 mt-1">
                    {COUNTRY_CODES[countryCode as keyof typeof COUNTRY_CODES].name}
                </div>
            )}
        </div>
    );
};
