'use client';

import <PERSON><PERSON><PERSON><PERSON> from 'react-json-view';
import { Deployment, DeploymentStatus, DeploymentType } from '@/types/deployments';
import { isJson } from '@/common/isJson';
import { ModalLogicShowLogs } from '@/components/Modals';
import { useLogsStore } from '@/store/logsStore';
import { useEffect, useState } from 'react';
import { ItemBoxBottomRadius } from '../ItemBox';
import { useGetAvailableVersions } from '@/hooks/useGetAvailableVersions';
import { Tabs } from './Tabs';
import { DangerZone } from './DangerZone';

enum ISSUER_TABS_KEYS {
    DETAILS = 'details',
    DID_DOCUMENT = 'did_document',
    LOGS = 'logs',
    INSTRUCTION = 'instruction',
    UPGRADE_VERSION = 'upgrade_version',
    ACTIONS = 'actions',
}

interface Props {
    type: DeploymentType;
    status: DeploymentStatus;
    deployment: Deployment;
    handleDelete: () => void;
    handleUpgradeSecret: () => void;
    handleUpgradeVersion: () => void;
}

const API_DOCS_PREFIX = '/api-docs';

const formatKey = (key: string): string => {
    const withSpaces = key.replace(/([a-z0-9])([A-Z])/g, '$1 $2').replace(/[_-]+/g, ' ');
    return withSpaces
        .split(' ')
        .map(w => w.charAt(0).toUpperCase() + w.slice(1))
        .join(' ');
};

const getInstructionLink = (type: DeploymentType): string => {
    return `https://docs.empe.io/develop/${type}`;
};

export const WidgetDetailsOfDeploymentIssuer = ({
    type,
    deployment,
    handleDelete,
    handleUpgradeSecret,
    handleUpgradeVersion,
}: Props) => {
    const [activeTab, setActiveTab] = useState<ISSUER_TABS_KEYS>(ISSUER_TABS_KEYS.DETAILS);
    const { setLogs } = useLogsStore();
    const { versions } = useGetAvailableVersions({
        type: type,
        deploymentCurrentVersion: deployment.version,
    });

    useEffect(() => {
        setLogs([]);
    }, [setLogs]);

    const ISSUER_TABS = [
        {
            label: 'Details of issuer',
            key: ISSUER_TABS_KEYS.DETAILS,
        },
        {
            label: 'Did Document',
            key: ISSUER_TABS_KEYS.DID_DOCUMENT,
        },
        {
            label: 'Logs',
            key: ISSUER_TABS_KEYS.LOGS,
        },
        {
            label: 'Instruction',
            href: getInstructionLink(type),
        },
        // {
        //     label: 'Upgrade Version',
        //     hidden: versions.length <= 0,
        //     onClick: handleUpgradeVersion,
        // },
        {
            label: 'Actions',
            key: ISSUER_TABS_KEYS.ACTIONS,
        },
    ];

    const renderDetails = () => {
        return (
            <div className="flex flex-col h-full overflow-auto without-scrollbar">
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('id')}</div>
                    <span className="text-md">{deployment.id}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('fullHost')}</div>
                    <span className="text-md">{deployment.fullHost}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('swaggerApi')}</div>
                    <a
                        className="text-md text-blue-500 hover:text-blue-600"
                        target="_blank"
                        href={deployment.fullHost + API_DOCS_PREFIX}
                    >
                        {deployment.fullHost + API_DOCS_PREFIX}
                    </a>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('status')}</div>
                    <span className="text-md">{deployment.status}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('networkName')}</div>
                    <span className="text-md">{deployment.networkName}</span>
                </div>
                <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                    <div className="text-xs text-main-600/50">{formatKey('version')}</div>
                    <span className="text-md">{deployment.version}</span>
                </div>
                {deployment.issuerName && (
                    <>
                        <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                            <div className="text-xs text-main-600/50">{formatKey('issuerName')}</div>
                            <span className="text-md">{deployment.issuerName}</span>
                        </div>
                        <div className="flex w-full flex-col justify-center  border-gray-700 px-10 py-2 gap-2">
                            <div className="text-xs text-main-600/50">{formatKey('issuerCreatorAddress')}</div>
                            <span className="text-md">{deployment.issuerCreatorAddress}</span>
                        </div>
                    </>
                )}
            </div>
        );
    };

    const renderDidDocument = () => {
        if (!deployment.didDocument) return null;
        return (
            <div className="flex w-full flex-col justify-center border-b border-gray-700 px-10 py-2 gap-2">
                <div className="text-xs text-main-600/50">{formatKey('didDocument')}</div>
                {isJson(deployment.didDocument) ? (
                    <div className="font-mono text-sm">
                        <ReactJson
                            src={JSON.parse(deployment.didDocument)}
                            theme="twilight"
                            style={{ background: 'transparent' }}
                            displayDataTypes={false}
                            collapsed={8}
                            name={false}
                        />
                    </div>
                ) : (
                    <span className="text-md">{deployment.didDocument}</span>
                )}
            </div>
        );
    };

    const renderLogs = () => {
        return <ModalLogicShowLogs deploymentId={deployment.id} type={type} />;
    };

    const renderActions = () => {
        return (
            <DangerZone
                updateVersionAvailable={versions.length > 0}
                handleDelete={handleDelete}
                handleUpgradeSecret={handleUpgradeSecret}
                handleUpgradeVersion={handleUpgradeVersion}
            />
        );
    };

    return (
        <div>
            <Tabs activeTab={activeTab} setActiveTab={setActiveTab} tabs={ISSUER_TABS} />
            <ItemBoxBottomRadius>
                <div className="py-box-50">
                    {activeTab === ISSUER_TABS_KEYS.DETAILS && renderDetails()}
                    {activeTab === ISSUER_TABS_KEYS.DID_DOCUMENT && renderDidDocument()}
                    {activeTab === ISSUER_TABS_KEYS.LOGS && renderLogs()}
                    {activeTab === ISSUER_TABS_KEYS.ACTIONS && renderActions()}
                </div>
            </ItemBoxBottomRadius>
        </div>
    );
};
