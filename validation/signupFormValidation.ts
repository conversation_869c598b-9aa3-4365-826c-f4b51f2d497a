import { z } from 'zod';
import {
    validateCheckbox,
    validateEmail,
    validateName,
    validateOrganizationName,
    validatePassword,
    validatePasswordsMatch, validatePhoneNumber,
    validateRepeatPassword,
} from './common';
import { FormFieldsTypes } from '@/common/formFieldsTypes';

export const createSignupSchema = (t: (key: string) => string) =>
    z.object({
        email: validateEmail(t),
        phoneNumber: validatePhoneNumber(t),
        password: validatePassword(t),
        repeatPassword: validateRepeatPassword(t),
        firstName: validateName(t),
        lastName: validateName(t),
        organizationName: validateOrganizationName(t),
        termsAndConditions: validateCheckbox(t),
    });

export const createSignupSchemaWithRefine = (t: (key: string) => string) =>
    createSignupSchema(t).superRefine(validatePasswordsMatch(t));

export type SignupFormData = z.infer<ReturnType<typeof createSignupSchema>>;

export const SignupFormInputsTypes: Record<keyof SignupFormData, FormFieldsTypes> = {
    email: FormFieldsTypes.INPUT,
    password: FormFieldsTypes.PASSWORD,
    phoneNumber: FormFieldsTypes.INPUT,
    repeatPassword: FormFieldsTypes.PASSWORD,
    firstName: FormFieldsTypes.INPUT,
    lastName: FormFieldsTypes.INPUT,
    organizationName: FormFieldsTypes.INPUT,
    termsAndConditions: FormFieldsTypes.CHECKBOX,
};
